#include <stdint.h>
#include <stddef.h>
#include <stdlib.h>
#include "/src/libspng/tests/spng_write_fuzzer.c"

extern "C" int LLVMFuzzerTestOneInput(const uint8_t *data, size_t size) {
    if (size < 4) {
        return 0;
    }

    // TODO: Parse input data and call size_t LLVMFuzzerCustomMutator(uint8_t *, size_t, size_t, unsigned int)
    // Example:
    // LLVMFuzzerCustomMutator(/* parameters from data */);

    return 0;
}